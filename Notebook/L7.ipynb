import requests

# 确保你的FastAPI服务器正在运行！

# 测试第一个端点: /
try:
    response_root = requests.get("http://127.0.0.1:8000/")
    print("请求 / 的结果:", response_root.json())
except requests.ConnectionError:
    print("连接服务器失败，请确保 uvicorn main:app --reload 正在运行！")

# 测试第二个端点: /greet
try:
    # 构造带参数的URL
    params = {'name': '工程师'}
    response_greet = requests.get("http://127.0.0.1:8000/greet", params=params)
    print("请求 /greet 的结果:", response_greet.json())
except requests.ConnectionError:
    print("连接服务器失败！")